<script setup lang="ts">
import { Head, <PERSON> } from '@inertiajs/vue3';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Separator } from '@/components/ui/separator';
import { 
  ArrowLeft, 
  CheckCircle,
  XCircle,
  Clock,
  Copy,
  Code
} from 'lucide-vue-next';
import { toast } from 'sonner';
import AppLayout from '@/layouts/AppLayout.vue';
import type { WebhookEndpoint, WebhookDelivery } from '@/types/webhook';
import type { BreadcrumbItemType } from '@/types';

interface Props {
  webhook: WebhookEndpoint;
  delivery: WebhookDelivery;
}

const props = defineProps<Props>();

const breadcrumbs: BreadcrumbItemType[] = [
  {
    title: 'Webhook 管理',
    href: '/webhooks',
  },
  {
    title: props.webhook.name,
    href: `/webhooks/${props.webhook.id}`,
  },
  {
    title: '发送记录',
    href: `/webhooks/${props.webhook.id}/deliveries/${props.delivery.id}`,
  },
];

const getStatusBadge = (status: string) => {
  switch (status) {
    case 'success':
      return { variant: 'default' as const, text: '成功', icon: CheckCircle, color: 'text-green-600' };
    case 'failed':
      return { variant: 'destructive' as const, text: '失败', icon: XCircle, color: 'text-red-600' };
    case 'pending':
      return { variant: 'secondary' as const, text: '待处理', icon: Clock, color: 'text-yellow-600' };
    default:
      return { variant: 'secondary' as const, text: status, icon: Clock, color: 'text-gray-600' };
  }
};

const copyToClipboard = async (text: string, label: string) => {
  try {
    await navigator.clipboard.writeText(text);
    toast.success('已复制', {
      description: `${label}已复制到剪贴板`
    });
  } catch (error) {
    toast.error('复制失败', {
      description: '无法访问剪贴板'
    });
  }
};

const formatJson = (obj: any) => {
  return JSON.stringify(obj, null, 2);
};

const formatDate = (date: string) => {
  return new Date(date).toLocaleString('zh-CN');
};
</script>

<template>
  <Head :title="`发送记录 #${delivery.id}`" />
  
  <AppLayout :breadcrumbs="breadcrumbs">
    <div class="p-4 space-y-6">
      <!-- 头部 -->
      <div>
        <div class="flex items-center gap-3">
          <h1 class="text-3xl font-bold">发送记录 #{{ delivery.id }}</h1>
          <component 
            :is="getStatusBadge(delivery.status).icon"
            :class="[getStatusBadge(delivery.status).color, 'h-6 w-6']"
          />
          <Badge :variant="getStatusBadge(delivery.status).variant">
            {{ getStatusBadge(delivery.status).text }}
          </Badge>
        </div>
        <p class="text-muted-foreground mt-2">
          {{ webhook.name }} • {{ delivery.event_type }}
        </p>
      </div>
    </div>

    <!-- 主要内容 -->
    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
      <!-- 主内容区域 -->
      <div class="lg:col-span-2 space-y-6">
        <!-- 请求详情 -->
        <Card>
          <CardHeader>
            <CardTitle>请求详情</CardTitle>
            <CardDescription>
              发送到 {{ webhook.url }} 的请求信息
            </CardDescription>
          </CardHeader>
          <CardContent class="space-y-4">
            <div class="grid grid-cols-2 gap-4">
              <div>
                <h4 class="font-medium mb-2">事件类型</h4>
                <Badge variant="outline">{{ delivery.event_type }}</Badge>
              </div>
              
              <div>
                <h4 class="font-medium mb-2">尝试次数</h4>
                <span class="text-sm">{{ delivery.attempt }} / {{ webhook.max_attempts }}</span>
              </div>
              
              <div>
                <h4 class="font-medium mb-2">发送时间</h4>
                <span class="text-sm text-muted-foreground">
                  {{ formatDate(delivery.created_at) }}
                </span>
              </div>
              
              <div v-if="delivery.delivered_at">
                <h4 class="font-medium mb-2">完成时间</h4>
                <span class="text-sm text-muted-foreground">
                  {{ formatDate(delivery.delivered_at) }}
                </span>
              </div>
            </div>
          </CardContent>
        </Card>

        <!-- 响应详情 -->
        <Card v-if="delivery.status !== 'pending'">
          <CardHeader>
            <div class="flex items-center justify-between">
              <div>
                <CardTitle>响应详情</CardTitle>
                <CardDescription>
                  来自目标服务器的响应信息
                </CardDescription>
              </div>
              <Button 
                variant="outline" 
                size="sm" 
                @click="copyToClipboard(delivery.response_body || '', '响应内容')"
                v-if="delivery.response_body"
              >
                <Copy class="mr-2 h-4 w-4" />
                复制响应
              </Button>
            </div>
          </CardHeader>
          <CardContent class="space-y-4">
            <div class="grid grid-cols-2 gap-4">
              <div v-if="delivery.http_status_code">
                <h4 class="font-medium mb-2">HTTP 状态码</h4>
                <Badge 
                  :variant="delivery.http_status_code >= 200 && delivery.http_status_code < 300 ? 'default' : 'destructive'"
                  class="font-mono"
                >
                  {{ delivery.http_status_code }}
                </Badge>
              </div>
              
              <div v-if="delivery.formatted_duration">
                <h4 class="font-medium mb-2">响应时间</h4>
                <span class="text-sm">{{ delivery.formatted_duration }}</span>
              </div>
            </div>
            
            <div v-if="delivery.response_body">
              <h4 class="font-medium mb-2">响应内容</h4>
              <div class="border rounded-lg p-4 bg-muted/50">
                <pre class="text-sm overflow-x-auto whitespace-pre-wrap break-words">{{ delivery.response_body }}</pre>
              </div>
            </div>
            
            <div v-if="delivery.error_message">
              <h4 class="font-medium mb-2 text-destructive">错误信息</h4>
              <div class="border rounded-lg p-4 bg-destructive/5 border-destructive/20">
                <p class="text-sm text-destructive">{{ delivery.error_message }}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <!-- 请求载荷 -->
        <Card>
          <CardHeader>
            <div class="flex items-center justify-between">
              <div>
                <CardTitle class="flex items-center gap-2">
                  <Code class="h-5 w-5" />
                  请求载荷
                </CardTitle>
                <CardDescription>
                  发送的 JSON 数据内容
                </CardDescription>
              </div>
              <Button 
                variant="outline" 
                size="sm" 
                @click="copyToClipboard(formatJson(delivery.payload), '请求载荷')"
              >
                <Copy class="mr-2 h-4 w-4" />
                复制 JSON
              </Button>
            </div>
          </CardHeader>
          <CardContent>
            <div class="border rounded-lg p-4 bg-muted/50">
              <pre class="text-sm overflow-x-auto">{{ formatJson(delivery.payload) }}</pre>
            </div>
          </CardContent>
        </Card>
      </div>

      <!-- 侧边栏信息 -->
      <div class="space-y-6">
        <!-- 状态概览 -->
        <Card>
          <CardHeader>
            <CardTitle>状态概览</CardTitle>
          </CardHeader>
          <CardContent class="space-y-4">
            <div class="flex items-center justify-center p-6">
              <div class="text-center">
                <component 
                  :is="getStatusBadge(delivery.status).icon"
                  :class="[getStatusBadge(delivery.status).color, 'h-12 w-12 mx-auto mb-2']"
                />
                <div class="text-lg font-semibold">
                  {{ getStatusBadge(delivery.status).text }}
                </div>
                <div class="text-sm text-muted-foreground">
                  第 {{ delivery.attempt }} 次尝试
                </div>
              </div>
            </div>
            
            <Separator />
            
            <div class="space-y-3 text-sm">
              <div class="flex justify-between">
                <span class="font-medium">创建时间</span>
                <span class="text-muted-foreground">
                  {{ formatDate(delivery.created_at) }}
                </span>
              </div>
              
              <div v-if="delivery.delivered_at" class="flex justify-between">
                <span class="font-medium">完成时间</span>
                <span class="text-muted-foreground">
                  {{ formatDate(delivery.delivered_at) }}
                </span>
              </div>
              
              <div v-if="delivery.duration" class="flex justify-between">
                <span class="font-medium">耗时</span>
                <span class="text-muted-foreground">
                  {{ delivery.formatted_duration }}
                </span>
              </div>
            </div>
          </CardContent>
        </Card>

        <!-- Webhook 信息 -->
        <Card>
          <CardHeader>
            <CardTitle>Webhook 信息</CardTitle>
          </CardHeader>
          <CardContent class="space-y-3 text-sm">
            <div>
              <span class="font-medium">名称：</span>
              <Link 
                :href="route('webhooks.show', webhook.id)"
                class="text-primary hover:underline"
              >
                {{ webhook.name }}
              </Link>
            </div>
            
            <div>
              <span class="font-medium">URL：</span>
              <span class="text-muted-foreground break-all font-mono text-xs">
                {{ webhook.url }}
              </span>
            </div>
            
            <div>
              <span class="font-medium">超时设置：</span>
              <span class="text-muted-foreground">
                {{ webhook.timeout }} 秒
              </span>
            </div>
            
            <div>
              <span class="font-medium">最大重试：</span>
              <span class="text-muted-foreground">
                {{ webhook.max_attempts }} 次
              </span>
            </div>
          </CardContent>
        </Card>

        <!-- 快速操作 -->
        <Card>
          <CardHeader>
            <CardTitle>快速操作</CardTitle>
          </CardHeader>
          <CardContent class="space-y-2">
            <Button variant="outline" class="w-full" as-child>
              <Link :href="route('webhooks.show', webhook.id)">
                返回 Webhook 详情
              </Link>
            </Button>
            
            <Button variant="outline" class="w-full" as-child>
              <Link :href="route('webhooks.index')">
                返回 Webhook 列表
              </Link>
            </Button>
          </CardContent>
        </Card>
      </div>
    </div>
  </AppLayout>
</template>
