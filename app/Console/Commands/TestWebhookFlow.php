<?php

namespace App\Console\Commands;

use App\Events\K8s\DeploymentCreated;
use App\Models\Workspace;
use Illuminate\Console\Command;

class TestWebhookFlow extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'webhooks:test-flow';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Test the complete webhook flow with a K8s event';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('测试 Webhook 完整流程...');

        // 查找第一个workspace
        $workspace = Workspace::first();
        if (! $workspace) {
            $this->error('没有找到 workspace，请先创建一个 workspace');

            return 1;
        }

        $this->info("使用 workspace: {$workspace->name} (namespace: {$workspace->namespace})");

        // 检查是否有webhook端点
        $webhookCount = $workspace->webhookEndpoints()->where('is_active', true)->count();
        $this->info("活跃的 webhook 端点数量: {$webhookCount}");

        if ($webhookCount === 0) {
            // 创建一个测试webhook端点
            $webhook = $workspace->webhookEndpoints()->create([
                'name' => 'Test Deployment Webhook',
                'url' => 'https://httpbin.org/post',
                'events' => ['deployment.created', '*'],
                'is_active' => true,
            ]);

            $this->info("创建了测试 webhook 端点: {$webhook->name} (ID: {$webhook->id})");
        }

        // 模拟一个 K8s deployment.created 事件
        $this->info('触发 deployment.created 事件...');

        $deploymentEvent = new DeploymentCreated(
            namespace: $workspace->namespace,
            clusterName: $workspace->cluster->name ?? 'test-cluster',
            clusterId: $workspace->cluster_id ?? 1,
            resourceName: 'test-deployment',
            resource: [
                'apiVersion' => 'apps/v1',
                'kind' => 'Deployment',
                'metadata' => [
                    'name' => 'test-deployment',
                    'namespace' => $workspace->namespace,
                ],
                'spec' => [
                    'replicas' => 3,
                    'selector' => [
                        'matchLabels' => [
                            'app' => 'test-app',
                        ],
                    ],
                ],
            ]
        );

        // 触发事件
        event($deploymentEvent);

        $this->info('事件已触发！');
        $this->info('检查 webhook_deliveries 表查看发送结果');
        $this->info('你也可以检查 https://httpbin.org 的响应');

        return 0;
    }
}
