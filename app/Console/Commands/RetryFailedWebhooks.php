<?php

namespace App\Console\Commands;

use App\Service\WebhookService;
use Illuminate\Console\Command;

class RetryFailedWebhooks extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'webhooks:retry-failed';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Retry failed webhook deliveries';

    public function __construct(
        private WebhookService $webhookService
    ) {
        parent::__construct();
    }

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('开始重试失败的 Webhook 发送...');

        $this->webhookService->retryFailedDeliveries();

        $this->info('重试任务完成！');

        return 0;
    }
}
