<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class WebhookDeliveryResource extends JsonResource
{
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'event_type' => $this->event_type,
            'status' => $this->status,
            'http_status_code' => $this->http_status_code,
            'response_body' => $this->response_body,
            'error_message' => $this->error_message,
            'attempt' => $this->attempt,
            'duration' => $this->duration,
            'formatted_duration' => $this->formatted_duration,
            'payload' => $this->payload,
            'delivered_at' => $this->delivered_at,
            'created_at' => $this->created_at,
            'updated_at' => $this->updated_at,
            'webhook_endpoint_id' => $this->webhook_endpoint_id,
        ];
    }
}
