<?php

namespace App\Listeners;

use App\Events\K8s\BaseK8sResourceEvent;
use App\Models\WebhookEndpoint;
use App\Service\WebhookService;
use Illuminate\Support\Facades\Log;

class WebhookEventListener
{
    public function __construct(
        private WebhookService $webhookService
    ) {}

    /**
     * Handle the event.
     */
    public function handle(BaseK8sResourceEvent $event): void
    {
        try {
            // 构建事件类型名称，例如：deployment.created, service.updated
            $eventType = "{$event->resourceType}.{$event->action}";

            Log::debug('Processing webhook event', [
                'event_type' => $eventType,
                'namespace' => $event->namespace,
                'resource_name' => $event->resourceName,
            ]);

            // 查找对应workspace的所有活跃webhook端点
            $webhookEndpoints = WebhookEndpoint::whereHas('workspace', function ($query) use ($event) {
                $query->where('namespace', $event->namespace);
            })
                ->where('is_active', true)
                ->get();

            if ($webhookEndpoints->isEmpty()) {
                Log::debug('No active webhook endpoints found for namespace', [
                    'namespace' => $event->namespace,
                ]);

                return;
            }

            // 为每个匹配的webhook端点发送事件
            foreach ($webhookEndpoints as $endpoint) {
                if ($endpoint->shouldReceiveEvent($eventType) || $endpoint->shouldReceiveEvent('*')) {
                    Log::debug('Sending webhook for event', [
                        'endpoint_id' => $endpoint->id,
                        'endpoint_name' => $endpoint->name,
                        'event_type' => $eventType,
                    ]);

                    $this->webhookService->sendWebhook($endpoint, $eventType, [
                        'event_id' => uniqid(),
                        'event_type' => $eventType,
                        'namespace' => $event->namespace,
                        'cluster' => [
                            'id' => $event->clusterId,
                            'name' => $event->clusterName,
                        ],
                        'resource' => [
                            'type' => $event->resourceType,
                            'name' => $event->resourceName,
                            'data' => $event->resource,
                        ],
                        'action' => $event->action,
                        'timestamp' => now()->toISOString(),
                    ]);
                }
            }

        } catch (\Exception $e) {
            Log::error('Error processing webhook event', [
                'error' => $e->getMessage(),
                'event_type' => $eventType ?? 'unknown',
                'trace' => $e->getTraceAsString(),
            ]);

            // 重新抛出异常以触发重试机制
            throw $e;
        }
    }

    /**
     * Handle a job failure.
     */
    public function failed(BaseK8sResourceEvent $event, \Throwable $exception): void
    {
        Log::error('Webhook event listener failed', [
            'event_type' => "{$event->resourceType}.{$event->action}",
            'namespace' => $event->namespace,
            'error' => $exception->getMessage(),
        ]);
    }
}
