<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class WebhookDelivery extends Model
{
    use HasFactory;

    protected $fillable = [
        'webhook_endpoint_id',
        'url',
        'event_type',
        'payload',
        'headers',
        'status',
        'attempts',
        'response_status',
        'response_body',
        'error_message',
        'delivered_at',
        'next_retry_at',
    ];

    protected $casts = [
        'payload' => 'array',
        'headers' => 'array',
        'delivered_at' => 'datetime',
        'next_retry_at' => 'datetime',
    ];

    /**
     * 关联到 webhook endpoint
     */
    public function webhookEndpoint(): BelongsTo
    {
        return $this->belongsTo(WebhookEndpoint::class);
    }

    /**
     * 标记为成功
     */
    public function markAsSuccessful(int $responseStatus, string $responseBody): void
    {
        $this->update([
            'status' => 'success',
            'response_status' => $responseStatus,
            'response_body' => $responseBody,
            'delivered_at' => now(),
            'next_retry_at' => null,
        ]);
    }

    /**
     * 标记为失败
     */
    public function markAsFailed(string $errorMessage, ?int $nextRetryIn = null): void
    {
        $this->increment('attempts');

        $this->update([
            'status' => 'failed',
            'error_message' => $errorMessage,
            'next_retry_at' => $nextRetryIn ? now()->addSeconds($nextRetryIn) : null,
        ]);
    }

    /**
     * 检查是否应该重试
     */
    public function shouldRetry(): bool
    {
        return $this->status === 'failed'
            && $this->attempts < $this->webhookEndpoint->max_attempts
            && $this->next_retry_at
            && $this->next_retry_at <= now();
    }
}
