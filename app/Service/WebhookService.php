<?php

namespace App\Service;

use App\Models\WebhookDelivery;
use App\Models\WebhookEndpoint;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

class WebhookService
{
    /**
     * 发送webhook到指定端点
     */
    public function sendWebhook(WebhookEndpoint $endpoint, string $eventType, array $payload): WebhookDelivery
    {
        // 创建delivery记录
        $delivery = WebhookDelivery::create([
            'webhook_endpoint_id' => $endpoint->id,
            'url' => $endpoint->url,
            'event_type' => $eventType,
            'payload' => $payload,
            'headers' => $endpoint->headers ?? [],
            'status' => 'pending',
        ]);

        try {
            // 准备请求头
            $headers = array_merge(
                $endpoint->headers ?? [],
                [
                    'Content-Type' => 'application/json',
                    'User-Agent' => 'PaaS-Webhook/1.0',
                ]
            );

            // 如果有secret，添加签名
            if ($endpoint->secret) {
                $signature = hash_hmac('sha256', json_encode($payload), $endpoint->secret);
                $headers['X-Webhook-Signature'] = 'sha256='.$signature;
            }

            // 发送请求
            $response = Http::withHeaders($headers)
                ->timeout($endpoint->timeout)
                ->post($endpoint->url, $payload);

            // 更新delivery状态
            if ($response->successful()) {
                $delivery->markAsSuccessful($response->status(), $response->body());

                // 更新endpoint的最后发送时间
                $endpoint->update(['last_delivered_at' => now()]);

                Log::info('Webhook delivered successfully', [
                    'endpoint_id' => $endpoint->id,
                    'delivery_id' => $delivery->id,
                    'status' => $response->status(),
                ]);
            } else {
                $delivery->markAsFailed(
                    "HTTP {$response->status()}: {$response->body()}",
                    $this->getRetryDelay($delivery->attempts + 1)
                );

                Log::warning('Webhook delivery failed', [
                    'endpoint_id' => $endpoint->id,
                    'delivery_id' => $delivery->id,
                    'status' => $response->status(),
                    'response' => $response->body(),
                ]);
            }

        } catch (\Exception $e) {
            $delivery->markAsFailed(
                $e->getMessage(),
                $this->getRetryDelay($delivery->attempts + 1)
            );

            Log::error('Webhook delivery exception', [
                'endpoint_id' => $endpoint->id,
                'delivery_id' => $delivery->id,
                'error' => $e->getMessage(),
            ]);
        }

        return $delivery;
    }

    /**
     * 重试失败的webhook发送
     */
    public function retryFailedDeliveries(): void
    {
        $failedDeliveries = WebhookDelivery::where('status', 'failed')
            ->where('next_retry_at', '<=', now())
            ->with('webhookEndpoint')
            ->get();

        foreach ($failedDeliveries as $delivery) {
            if ($delivery->shouldRetry()) {
                Log::info('Retrying webhook delivery', [
                    'delivery_id' => $delivery->id,
                    'attempt' => $delivery->attempts + 1,
                ]);

                // 不重新发送，而是更新为pending并让队列处理
                $delivery->update([
                    'status' => 'pending',
                    'next_retry_at' => null,
                ]);
            }
        }
    }

    /**
     * 测试webhook端点
     */
    public function testEndpoint(WebhookEndpoint $endpoint): WebhookDelivery
    {
        $testPayload = [
            'event_type' => 'test',
            'message' => 'This is a test webhook from PaaS',
            'webhook_endpoint' => [
                'id' => $endpoint->id,
                'name' => $endpoint->name,
            ],
            'timestamp' => now()->toISOString(),
        ];

        return $this->sendWebhook($endpoint, 'test', $testPayload);
    }

    /**
     * 计算重试延迟时间（指数退避）
     */
    private function getRetryDelay(int $attempt): int
    {
        // 指数退避：1分钟、2分钟、4分钟、8分钟等
        return min(60 * pow(2, $attempt - 1), 3600); // 最大1小时
    }
}
