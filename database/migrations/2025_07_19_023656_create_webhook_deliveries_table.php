<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('webhook_deliveries', function (Blueprint $table) {
            $table->id();
            $table->foreignId('webhook_endpoint_id')->constrained()->onDelete('cascade');
            $table->string('url');
            $table->string('event_type'); // 事件类型，如 deployment.created
            $table->json('payload'); // 发送的数据
            $table->json('headers')->nullable(); // 发送的头部
            $table->enum('status', ['pending', 'success', 'failed'])->default('pending');
            $table->integer('attempts')->default(0);
            $table->integer('response_status')->nullable(); // HTTP状态码
            $table->text('response_body')->nullable(); // 响应内容
            $table->text('error_message')->nullable(); // 错误信息
            $table->timestamp('delivered_at')->nullable(); // 发送时间
            $table->timestamp('next_retry_at')->nullable(); // 下次重试时间
            $table->timestamps();

            $table->index(['webhook_endpoint_id', 'status']);
            $table->index(['status', 'next_retry_at']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('webhook_deliveries');
    }
};
